# Enterprise Asset Management

This document describes the enterprise asset management functionality that allows enterprise clients to have global assets shared across all users in their instance.

## Overview

For enterprise clients, instead of having individual user custom assets, the system now supports instance-wide assets that are organized in a structured folder hierarchy similar to the main assets pack library.

## Key Features

### 1. **Automatic Enterprise Detection**
- Uses the existing `isEnterpriseClient()` method from UserProvider
- Checks `config.plan.name === 'enterprise'` first, then falls back to `user.meta.plan === 'enterprise'`

### 2. **Structured Asset Organization**
Enterprise assets are organized in the following folder structure:
```
enterprise_assets/
├── presets/        # VJSON presets
├── hdrmaps/        # Environment maps (metal/gem categories)
├── materials/      # Material assets (categorized)
├── maps/           # Additional maps
├── configs/        # Ground configurations
├── images/         # Background images
└── stages/         # Model stages
```

### 3. **Smart Upload Routing**
When an enterprise user uploads an asset through the editor:
- The system automatically determines the correct folder based on asset type
- Creates missing folders if they don't exist
- Applies proper category tags (e.g., `_category:metal`, `_category:gem`)
- Maintains the same tagging system as the main library

### 4. **Asset Type Mapping**
| Asset Type | Target Folder | Additional Tags |
|------------|---------------|-----------------|
| Environment | hdrmaps | `_category:metal` |
| GemEnvironment | hdrmaps | `_category:gem` |
| Background | images | - |
| Ground | configs | - |
| VJSON | presets | - |
| Material | materials | `_category:{materialCategory}` |
| Logo | presets | - |

## Implementation Details

### EnterpriseAssetsManager Class
Located in `src/provider/EnterpriseAssetsManager.ts`, this reusable class handles:

- **Folder Management**: Creates and maintains the enterprise asset folder structure
- **Asset Upload**: Routes uploads to correct folders with proper tags
- **Asset Retrieval**: Fetches enterprise assets and processes them
- **Library Processing**: Converts enterprise assets to the standard library format

### Key Methods

#### `uploadAsset(file, assetType, materialCategory?, thumbnailFile?)`
Uploads an asset to the appropriate enterprise folder with correct tags.

#### `getEnterpriseAssets()`
Retrieves all enterprise assets for the instance.

#### `processFiles(files)`
Processes enterprise assets into the standard AssetsLibrary format used by the editor.

#### `getEnterpriseAssetsForEditor()`
Returns enterprise assets in the format expected by the editor's `processFiles` method.

### AssetsProvider Integration
The existing AssetsProvider has been enhanced to:

- Detect enterprise clients automatically
- Route upload operations to enterprise asset management
- Return enterprise assets instead of user custom assets
- Maintain backward compatibility for non-enterprise users

## Usage

### For Developers

The enterprise asset management is transparent to existing code. The AssetsProvider automatically handles enterprise vs. user assets based on the client type.

```typescript
// This works for both enterprise and regular users
const { getCustomAssets, handleUploadCustomAsset } = useAssets();

// Upload an asset (automatically routed for enterprise)
await handleUploadCustomAsset({
  assetFile: file,
  assetType: "Environment",
  materialCategory: "metal"
});

// Get assets (returns enterprise assets for enterprise users)
const assets = await getCustomAssets();
```

### For Enterprise Clients

1. **Asset Upload**: When uploading assets through the editor, they will automatically be stored in the enterprise asset library
2. **Asset Access**: All users in the enterprise instance will see the same shared assets
3. **Organization**: Assets are automatically organized by type and category
4. **Compatibility**: Works with existing editor functionality without changes

## Backward Compatibility

- Non-enterprise users continue to use individual custom assets as before
- Existing custom asset functionality remains unchanged
- No breaking changes to the API or user interface

## Technical Notes

- Enterprise assets are tagged with `_enterprise_assets` for identification
- Folder creation is automatic and happens on-demand
- The system maintains the same tag structure as the main asset library
- Asset processing follows the same pattern as the editor's `processFiles` method
