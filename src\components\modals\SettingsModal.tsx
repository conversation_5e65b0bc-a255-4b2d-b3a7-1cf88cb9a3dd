import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { Modal } from "../Modal";
import { useUser } from "../../provider/UserProvider";
import { Select, SelectItem, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/react";
import toast from "react-hot-toast";
import { Button } from "../Button";
import { useModal } from "../../provider/useModal";

interface SettingsModalProps {
  onClose: () => void;
}

const SettingsModal: FC<SettingsModalProps> = (props) => {
  const { modalState, openPrompt, openConfirm } = useModal();
  const [users, setUsers] = useState<any[]>([]);
  const { api, user, can, isEnterpriseClient } = useUser();
  const [loading, setLoading] = useState(false);

  const loadUsers = useCallback(async () => {
    setLoading(true);
    const response = await api?.listUsers();
    if (response?.error || !response?.data) {
      console.log("Failed to load users", response);
      setLoading(false);
      return;
    }
    const data = response.data;
    data.forEach((user : any) => {
      try{
        user.meta = JSON.parse(user.meta);
    } catch (e) {
      user.meta = {};
    }
    });
    setUsers(data);
    setLoading(false);
  }, [api]);

  const handleSetRole = useCallback(
    async (selectedUser: string, selectedRole: string) => {
      if (!selectedUser || !selectedRole) return;
      if (!api || !(user as any)?.role?.endsWith("admin")) return;

      const userData = users.find((u) => u.id === selectedUser);
      if(!userData) return;

      const plan = await openConfirm(`Change ${userData.name ?? "user"}'s role to ${selectedRole}?`, { title: "Change iJewel Drive user role"});
      if (!plan) {
        return;
      }
      toast.loading("Setting role...", { id: "setRole" });
      api.updateUser({ id: selectedUser, role: selectedRole }).then((data) => {
        if (!data || data.error) {
          toast.error("Failed to update role", { id: "setRole" });
          console.log("Failed to update role", data);
          return;
        }
        toast.success("Role updated", { id: "setRole" });
        loadUsers();
      });
    },
    [api, users, user, openConfirm, loadUsers]
  );

  const handleSetBase = useCallback(
    async (selectedUser: string) => {
      if (!selectedUser) return;
      if (!api || !(user as any)?.role?.endsWith("admin")) return;

      const userData = users.find((u) => u.id === selectedUser);
      if(!userData) return;

      const base = await openPrompt("Enter base", { title: "Enter new base folder id", type: "text", placeholder: "new base"});
      if (!base) {
        toast.error("Base is required", { id: "setBase" });
        return;
      }
      toast.loading("Setting base...", { id: "setBase" });

      api.updateUser({ id: selectedUser, meta: {...userData.meta, base: base } }).then((data) => {
        if (!data || data.error) {
          toast.error("Failed to update base", { id: "setBase" });
          console.log("Failed to update base", data);
          return;
        }
        toast.success("Base updated", { id: "setBase" });
        loadUsers();
      });
    },
    [api, user, users, openPrompt, loadUsers]
  );


  const handleSetPlan = useCallback(
    async (selectedUser: string, selectedPlan: string) => {
      if (!selectedUser || !api) return
      if(!can("edit-user")) return;

      const userData = users.find((u) => u.id === selectedUser);
      if(!userData) return;

      const plan = await openConfirm(`Change ${userData.name ?? "user"}'s plan to ${selectedPlan}?`, { title: "Change iJewel Drive plan"});
      if (!plan) {
        return;
      }
      toast.loading("Setting plan...", { id: "setPlan" });
      api.updateUser({ id: selectedUser, meta: {...userData.meta, plan: selectedPlan } }).then((data) => {
        if (!data || data.error) {
          toast.error("Failed to update plan", { id: "setPlan" });
          console.log("Failed to update plan", data);
          return;
        }
        toast.success("Plan updated", { id: "setPlan" });
        loadUsers();
      });
    },
    [api, can, users, openConfirm, loadUsers]
  );

  const handleGrantAccess = useCallback(
    async (selectedUser: string) => {
      if (!selectedUser || !api) return;
      if (!isEnterpriseClient() || !(user as any)?.role?.endsWith("admin")) return;

      const userData = users.find((u) => u.id === selectedUser);
      if (!userData) return;

      const confirmed = await openConfirm(
        `Grant view access to ${userData.name ?? userData.email ?? "user"}?`,
        { title: "Grant View Access" }
      );
      if (!confirmed) {
        return;
      }

      toast.loading("Granting access...", { id: "grantAccess" });

      try {
        const updateUserPromise = api.updateUser({
          id: selectedUser,
          role: "viewer",
          meta: { ...userData.meta, base: "/", plan: "enterprise" }
        });

        const data = await updateUserPromise;
        if (!data || data.error) {
          toast.error("Failed to grant access", { id: "grantAccess" });
          console.log("Failed to grant access", data);
          return;
        }
        toast.success("Access granted successfully", { id: "grantAccess" });
        loadUsers();
      } catch (error) {
        toast.error("Failed to grant access", { id: "grantAccess" });
        console.log("Failed to grant access", error);
      }
    },
    [api, user, users, openConfirm, loadUsers, isEnterpriseClient]
  );

  useEffect(() => {
    if (modalState.modalType !== "SETTINGS") return;
    loadUsers();
  }, [api, loadUsers, modalState.modalType]);

  const allPlans = useMemo(()=>{
    return ["free", "start-up", "premium", "business", "enterprise"]
  },[]);

  const renderCell = useCallback(
    (item: any, columnKey: any) => {
      const cellValue = item[columnKey];

      switch (columnKey) {
        case "role":
          return (
            <div className="flex items-center gap-unit-lg">
              {item.email_verified ? (cellValue != "superadmin" ? <>
                  <Select
                    className="w-[120px]"
                    onChange={(e)=>{ (e.target.value != "") && handleSetRole(item.id, e.target.value)}}
                    placeholder={cellValue ?? "guest"}
                  >
                    <SelectItem value={"guest"} key={"guest"}>{"guest"}</SelectItem>
                    <SelectItem value={"viewer"} key={"viewer"}>{"viewer"}</SelectItem>
                    <SelectItem value={"editor"} key={"editor"}>{"editor"}</SelectItem>
                    <SelectItem value={"admin"} key={"admin"}>{"admin"}</SelectItem>
                  </Select>
                </> : <span className="text-center">{cellValue}</span>) : <span>Not verified</span>
                }
            </div>
          );
        case "base":
          return (
            <div className="flex items-center gap-unit-xl">
              {item.meta?.base ?? "Base not set"} <div className="flex-1"></div>
              <Button
                color="primary"
                className="h-unit-2xl ml-unit-md"
                fullWidth={false}
                name="Edit base"
                onClick={() => handleSetBase(item.id)}
              />
            </div>
          );
        case "plan":
          return (
            <div className="flex items-center gap-unit-xl">
              <Select
                className="w-[120px]"
                onChange={(e)=>{ (e.target.value != "") && (e.target.value != (item.meta?.plan ?? "free")) && handleSetPlan(item.id, e.target.value)}}
                placeholder={item.meta?.plan ?? "free"}
              >
                {allPlans.map((plan)=>(
                  <SelectItem value={plan} key={plan}>{plan}</SelectItem>
                ))}
              </Select>
            </div>
          )
        case "access": {
          if (!isEnterpriseClient()) return null;

          const isFreshUser = (!item.meta?.base) &&
                             item.role === "guest" &&
                             (item.meta?.plan === "free" || !item.meta?.plan);

          if (isFreshUser) {
            return (
              <Button
                color="primary"
                size="sm"
                className="h-unit-2xl"
                fullWidth={false}
                name="Grant Access"
                onClick={() => handleGrantAccess(item.id)}
              />
            );
          } else {
            return <span className="text-success text-sm font-medium">Access Granted</span>;
          }
        }
        default:
          return cellValue;
      }
    },[handleSetBase, handleSetPlan, handleSetRole, allPlans, handleGrantAccess, isEnterpriseClient]);

  return (
    <Modal
      isOpen={modalState.modalType == "SETTINGS"}
      onClose={props.onClose}
      size="full"
      backdrop="blur">
      {() => (
        <div className="w-full bg-white p-unit-xl flex flex-col overflow-y-auto">
          <p className="text-xl font-bold">Users</p>
          <Table
            aria-label="Example table with dynamic content"
            className="overflow-x-hidden">
            <TableHeader
              columns={columns.filter((col) => {
                // if(basename != "drive-weur-1" && basename != "dev-2") return col.key != "plan"
                // enterprise uers are not allowed to change plan and hence cant upload
                if(isEnterpriseClient()) return col.key != "plan";
                if (col.key === "access" && !isEnterpriseClient()) return false;
                return true;
              })}>
              {(column) => (
                <TableColumn key={column.key}>{column.label}</TableColumn>
              )}
            </TableHeader>
            <TableBody
              items={users.filter((t) => t.email != "<EMAIL>")}
              isLoading={loading}>
              {(item) => (
                <TableRow key={item.key} className="">
                  {(columnKey) => (
                    <TableCell className="">
                      {renderCell(item, columnKey)}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </Modal>
  );
};

const columns = [
  {
    key: "username",
    label: "USERNAME",
  },
  {
    key: "name",
    label: "NAME",
  },
  {
    key: "email",
    label: "EMAIL",
  },
  {
    key: "base",
    label: "Base Folder",
  },
  {
    key: "role",
    label: "ROLE",
  },
  {
    key: "plan",
    label: "PLAN",
  },
  {
    key: "access",
    label: "ACCESS",
  },
];

export default SettingsModal;
