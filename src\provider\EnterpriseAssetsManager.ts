import { CustomFileData, FileAPI } from "../api/api";
import { AssetsLibrary, CUSTOM_ASSET_ITEM, CUSTOM_ASSET_KEYS } from "../components/shared/types";
import { joinPaths } from "../components/shared/util";

interface IAsset {
  id: string;
  path: string;
  icon: string;
  name: string;
  isPremium: boolean;
  isDefault: boolean;
  tags: string[];
}

interface DriveFile {
  id: string;
  name: string;
  path?: string;
  file?: string;
  thumb: string;
  tags?: string;
  deleted?: boolean;
}

export class EnterpriseAssetsManager {
  private api: FileAPI;
  private enterpriseAssetsFolder: CustomFileData | null = null;
  private mainFolderNames = ["presets", "hdrmaps", "materials", "maps", "configs", "images", "stages"];

  constructor(api: FileAPI) {
    this.api = api;
  }

  private getTagsArray(tags: string | undefined): string[] {
    return tags ? tags.split(",").map((tag) => tag.trim()) : [];
  }

  private getCategoryTags(tagsArray: string[]): string[] {
    return tagsArray
      .filter((tag) => tag.startsWith("_category:"))
      .map((tag) => tag.replace("_category:", ""));
  }

  private getAssets(files: DriveFile[]): IAsset[] {
    return files.map((file) => {
      const tags = this.getTagsArray(file.tags);
      const isPremium = tags.includes("_premium");
      const isDefault = tags.includes("_default");

      return {
        id: file.id,
        path: file.file || `//${file.id}`,
        icon: file.thumb,
        name: file.name,
        isPremium,
        isDefault,
        tags,
      };
    });
  }

  private getAssetsAsStrings(files: DriveFile[]): string[] {
    return files.map((file) => {
      const tags = this.getTagsArray(file.tags);
      const isPremium = tags.includes("_premium");
      const isDefault = tags.includes("_default");

      const path = file.file || `//${file.id}`;
      const icon = file.thumb;

      return `${path};${icon}${isPremium ? ";premium" : ""}${isDefault ? ";default" : ""}`;
    });
  }

  async getEnterpriseAssetsFolder(): Promise<CustomFileData | null> {
    if (this.enterpriseAssetsFolder) return this.enterpriseAssetsFolder;
    
    const { data: existingFolders, error } = await this.api.getFilesBytag("_enterprise_assets");
    if (!error && existingFolders && existingFolders.length > 0) {
      this.enterpriseAssetsFolder = existingFolders[0];
      return this.enterpriseAssetsFolder;
    }

    const folderName = "enterprise_assets";
    const { data: newFolder, error: createError } = await this.api.createDirectory(
      folderName,
      this.api.basePath,
      undefined,
      true,
      "_enterprise_assets"
    );

    if (createError || !newFolder) {
      console.error("Failed to create enterprise assets folder:", createError);
      return null;
    }

    this.enterpriseAssetsFolder = newFolder;
    return newFolder;
  }

  async ensureMainFolders(): Promise<Record<string, CustomFileData>> {
    const parentFolder = await this.getEnterpriseAssetsFolder();
    if (!parentFolder) throw new Error("Could not get enterprise assets folder");

    const folders: Record<string, CustomFileData> = {};
    
    for (const folderName of this.mainFolderNames) {
      const { data: existingFiles } = await this.api.listFiles(parentFolder.path + parentFolder.id + "/");
      const existingFolder = existingFiles?.find(file => file.name === folderName && file.isDir);
      
      if (existingFolder) {
        folders[folderName] = existingFolder;
      } else {
        const { data: newFolder, error } = await this.api.createDirectory(
          folderName,
          parentFolder.path + parentFolder.id + "/",
          undefined,
          false
        );
        
        if (newFolder && !error) {
          folders[folderName] = newFolder;
        } else {
          console.error(`Failed to create folder ${folderName}:`, error);
        }
      }
    }
    
    return folders;
  }

  private getAssetFolderAndTags(assetType: CUSTOM_ASSET_KEYS, materialCategory?: string): { folder: string; tags: string } {
    let folder = "";
    let tags = `_cstype=${assetType}`;

    switch (assetType) {
      case "Environment":
      case "GemEnvironment":
        folder = "hdrmaps";
        if (assetType === "Environment") {
          tags += ",_category:metal";
        } else {
          tags += ",_category:gem";
        }
        break;
      case "Background":
        folder = "images";
        break;
      case "Ground":
        folder = "configs";
        break;
      case "VJSON":
        folder = "presets";
        break;
      case "Material":
        folder = "materials";
        if (materialCategory) {
          tags += `,_category:${materialCategory}`;
        }
        break;
      default:
        folder = "presets";
        break;
    }

    return { folder, tags };
  }

  async uploadAsset(
    file: File,
    assetType: CUSTOM_ASSET_KEYS,
    materialCategory?: string,
    thumbnailFile?: File
  ): Promise<CustomFileData | null> {
    const folders = await this.ensureMainFolders();
    const { folder: folderName, tags } = this.getAssetFolderAndTags(assetType, materialCategory);

    const targetFolder = folders[folderName];
    if (!targetFolder) {
      console.error(`Target folder ${folderName} not found`);
      return null;
    }

    const uploadPath = targetFolder.path + targetFolder.id + "/";
    const { data: asset, error } = await this.api.uploadFile(
      file,
      uploadPath,
      thumbnailFile,
      undefined,
      undefined,
      undefined,
      tags
    );

    if (error || !asset) {
      console.error("Failed to upload enterprise asset:", error);
      return null;
    }

    return asset;
  }

  async getEnterpriseAssets(): Promise<CustomFileData[]> {
    const parentFolder = await this.getEnterpriseAssetsFolder();
    if (!parentFolder) return [];

    const { data: assets, error } = await this.api.getAssets(parentFolder.id);
    if (error || !assets) {
      console.error("Failed to get enterprise assets:", error);
      return [];
    }

    return assets.filter((asset: CustomFileData) => !asset.deleted);
  }

  processFiles(files: CustomFileData[]): AssetsLibrary | undefined {
    const assets: AssetsLibrary = {
      presets: {} as any,
      materials: {} as any,
    };

    if (!files || files.length === 0) return undefined;

    const filteredFiles = files.filter((file) => !file.deleted);

    this.mainFolderNames.forEach((folderName) => {
      const folder = filteredFiles.find((file) => file.name === folderName);
      if (folder) {
        const children = filteredFiles.filter((file) => file.path?.includes?.(folder.id));

        switch (folderName) {
          case "presets":
            assets.presets!.VJSON = { basePath: "", assets: this.getAssetsAsStrings(children) };
            break;
          case "hdrmaps": {
            assets.presets!.Environment = {
              basePath: "",
              assets: this.getAssetsAsStrings(
                children.filter((file) => {
                  const tagsArray = this.getTagsArray(file.tags);
                  return tagsArray.includes("_category:metal");
                })
              ),
            };
            assets.presets!.GemEnvironment = {
              basePath: "",
              assets: this.getAssetsAsStrings(
                children.filter((file) => {
                  const tagsArray = this.getTagsArray(file.tags);
                  return tagsArray.includes("_category:gem");
                })
              ),
            };
            const remaining = children.filter((file) => {
              const tagsArray = this.getTagsArray(file.tags);
              return (
                !tagsArray.includes("_category:metal") &&
                !tagsArray.includes("_category:gem")
              );
            });
            if (remaining.length > 0) {
              if (!assets.presets!.Environment) {
                assets.presets!.Environment = { basePath: "", assets: [] };
              }
              assets.presets!.Environment.assets.push(...this.getAssetsAsStrings(remaining));
            }
            break;
          }
          case "materials": {
            const categories = new Set<string>();
            children.forEach((file) => {
              const tagsArray = this.getTagsArray(file.tags);
              this.getCategoryTags(tagsArray).forEach((category) => categories.add(category));
            });

            categories.forEach((category) => {
              const categoryAssets = children.filter((file) => {
                const tagsArray = this.getTagsArray(file.tags);
                const categories = this.getCategoryTags(tagsArray);
                return categories.includes(category);
              });
              const categoryKey = category as keyof AssetsLibrary["materials"];
              assets.materials![categoryKey] = {
                basePath: "",
                assets: this.getAssetsAsStrings(categoryAssets),
              };
            });

            const uncategorizedAssets = children.filter((file) => {
              const tagsArray = this.getTagsArray(file.tags);
              const categories = this.getCategoryTags(tagsArray);
              return categories.length === 0;
            });

            if (uncategorizedAssets.length > 0) {
              if (!assets.materials!.other) {
                assets.materials!.other = { basePath: "", assets: [] };
              }
              assets.materials!.other.assets.push(...this.getAssetsAsStrings(uncategorizedAssets));
            }
            break;
          }
          case "configs":
            assets.presets!.Ground = { basePath: "", assets: this.getAssetsAsStrings(children) };
            break;
          case "stages":
            assets.presets!.ModelStage = { basePath: "", assets: this.getAssetsAsStrings(children) };
            break;
          case "images":
            assets.presets!.Background = { basePath: "", assets: this.getAssetsAsStrings(children) };
            break;
          default:
            break;
        }
      }
    });

    return assets;
  }

  sanitizeCustomAsset(asset: CustomFileData): CUSTOM_ASSET_ITEM | null {
    const asset_url = this.api.getDownloadUrl(asset);
    if (!asset_url) {
      console.error("Asset url not found", asset);
      return null;
    }

    const isMaterial = asset.tags?.includes("_materialCategory");
    const materialCategory = isMaterial ? asset.tags?.split(",").find((tag) => tag.includes("_materialCategory"))?.split("=")[1] : null;

    const meta: any = {
      asset_data: asset.config,
      name: asset.name,
    };

    if (isMaterial) {
      if (!meta.asset_data) meta.asset_data = {};
      meta.asset_data.materialCategory = materialCategory;
    }

    return {
      id: asset.id,
      path: asset_url,
      size: asset.size,
      name: asset.name,
      icon: this.api.getDownloadUrl({ id: asset.id, file: asset.thumb }),
      isCustom: true,
      meta,
    };
  }

  async getEnterpriseAssetsForEditor(): Promise<DriveFile[]> {
    const assets = await this.getEnterpriseAssets();
    return assets.map(asset => ({
      id: asset.id,
      name: asset.name,
      path: asset.path,
      file: asset.file,
      thumb: asset.thumb || "",
      tags: asset.tags,
      deleted: asset.deleted || false
    }));
  }
}
