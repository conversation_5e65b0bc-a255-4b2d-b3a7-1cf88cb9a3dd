import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from 'tailwindcss';
import {viteSingleFile} from 'vite-plugin-singlefile'

// https://vitejs.dev/config/
export default defineConfig({
  base: "/",
  css: {
    postcss: {
      plugins: [tailwindcss()],
    },
  },
  server: {
    watch: {
      // re-run optimisation when files in node_modules/chonky/* change
      ignored: ['!**/node_modules/chonky/**']
    }
  },
  build: {
    rollupOptions: {
      // Add _all_ external dependencies here
      // external: ["chonky" , "@nextui-org/react"],
      // output: {
      //   globals: {
      //     webgi: "webgi",
      //   },
      // },
    },
    // commonjsOptions: {
    //   exclude: [/chonky/]
    // }
  },
  // optimizeDeps: {
  //   exclude: ['chonky'],
  // },
  plugins: [
    react(),
    viteSingleFile({
      // removeViteModuleLoader: true // todo @maan. see if this can be used for smaller builds. might break any inline import or something
    })
  ],

})
