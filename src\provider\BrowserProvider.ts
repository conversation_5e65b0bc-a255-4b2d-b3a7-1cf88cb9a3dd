/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, createElement, useCallback, useContext, useEffect, useRef, useState } from "react";
import { CustomFileData } from "../api/api";
import type { ViewerApp } from "webgi";
import { CustomFileMap } from "../hooks/fileHooks";
import { AssetsLibrary, QueueItem, QueueItemState } from "../components/shared/types";
import { UploadOptions } from "../components/UploadPrompt";
import { useUser } from "./UserProvider";
import { loadScript } from "../components/shared/util";
import { ThumbnailGenerator } from "../global";

export type CurrentView = 'files' | 'trash' | 'home' | 'settings';

const BrowserContext = createContext({
  viewer: null as ViewerApp | null,
  setViewer: (viewer: ViewerApp | null) => {},
  preview: null as CustomFileData | null,
  setPreview: (preview: CustomFileData | null) => {},
  fileQueue: [] as QueueItem[],
  updateQueue: (file: File, state: QueueItemState, progress: number, data?: any) => {},
  searchString: "",
  setSearchString: (searchString: string) => {},
  searchResults: [] as CustomFileData[],
  setSearchResults: (searchResults: any) => {},
  loading: false,
  setLoading: (loading: any) => {},
  fileMap: {} as CustomFileMap,
  setFileMap: ((fileMap: CustomFileMap | ((fileMap: CustomFileMap) => void)) => {}) as any,
  getThumbnailGenerator: (() => {} )as () => Promise<ThumbnailGenerator>,
  setThumbnailGenerator: (thumbnailGenerator: ThumbnailGenerator) => {},
  dragging: false,
  draggingRef: { current: false },
  setDragging: (dragging: boolean) => {},
  fileQueueRef: [] as QueueItem[],
  currentPath: "",
  setCurrentPath: (currentPath: string) => {},
  uploadOptions: null as UploadOptions | null,
  setUploadOptions: (uploadOptions: UploadOptions) => {},
  selectedFiles : new Set<string>,
  setSelectedFiles : (selectedFiles : Set<string>) => {},
  getAssets : () => {},
  addWebgi : (() => {} )as () => Promise<void>,
  showUploadProgress: false,
  setShowUploadProgress: (show: boolean) => {},
  currentView: 'files' as CurrentView,
  setCurrentView: (view: CurrentView) => {},
  isSideBarOpen: false,
  setIsSideBarOpen: (isOpen: boolean) => {},
});

export const useBrowser = () => useContext(BrowserContext);

export const fileQueueRef : QueueItem[] = [];
function useSetupBrowser() {
  const [preview, setPreview] = useState<CustomFileData | null>(null);
  const [viewer, setViewer] = useState<ViewerApp | null>(null);
  const [fileQueue, setFileQueue] = useState<QueueItem[]>([]);
  const [searchString, setSearchString] = useState<string>("");
  const [searchResults, setSearchResults] = useState<CustomFileData[]>([]);
  const [loading, setLoading] = useState(false);
  const [fileMap, setFileMap] = useState<CustomFileMap>({});
  const [thumbnailGenerator, setThumbnailGenerator] = useState<ThumbnailGenerator | null>(null);
  const [dragging, setDragging] = useState<boolean>(false);
  const draggingRef = useRef<boolean>(false);
  const [currentPath, setCurrentPath] = useState<string>("");
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [currentView, setCurrentView] = useState<CurrentView>('files');
  // const fileQueueRef = useRef<QueueItem[]>([]);
  const [uploadOptions, setUploadOptions] = useState<UploadOptions | null>({
    convert: true,
    autoCenter: true,
    autoScale: true,
    compress: true,
    encrypt: false,
    encryptionKey: ""
  });
  const {api , config} = useUser();
  const [showUploadProgress, setShowUploadProgress] = useState<boolean>(false);
  const [isSideBarOpen, setIsSideBarOpen] = useState<boolean>(false);

  useEffect(() => {
    draggingRef.current = dragging;
  }, [dragging]);


  const updateQueue = useCallback(
    (file: File, state: QueueItemState, progress: number, data?: any) => {
      if (fileQueueRef.length === 0 || state === 'uploading') {
        setShowUploadProgress(true);
      }
      
      setFileQueue(() => {
        if (fileQueueRef.find((item) => item.file.name === file.name ||
          (data?.id && (item.data?.id === data.id))) === undefined) {
            fileQueueRef.push({ file, state, progress, data });
            return [...fileQueueRef];
        }
        //file exist in queue
        const index = fileQueueRef.findIndex((item) => item.file.name === file.name ||
          (data?.id && (item.data?.id === data.id)));
        if (index !== -1) {
          fileQueueRef[index] = { file, state, progress,data: data ? {...fileQueueRef[index].data, ...data} : fileQueueRef[index].data };
        }
         return [...fileQueueRef];
      });
    },
    [setShowUploadProgress]
  );

  const getAssets = useCallback(async () => {
    if(!api) return;
    const { data , error} = await api.getAssets(config?.["assets-path"]);
    if(error || !data) {
      console.error("Error getting assets", error);
      return;
    }
    const assets = processAssets(data, config?.["files-api"] ? true : false);
    return assets;

  }, [api, config]);

  const addWebgi = useCallback(async () => {
    const version = config?.["webgi-version"];
    await loadScript(`https://dist.pixotronics.com/webgi/runtime/bundle-${version || "0.10.5"}.js`);
  }, [config]);

  const getThumbnailGenerator = useCallback(async () => {
    if (!thumbnailGenerator) {
      await addWebgi();
      //@ts-ignore 
      
      await loadScript("https://releases.ijewel3d.com/libs/thumbnail-generator/0.0.14/lib.umd.js");
      const ThumbnailGenerator = window['thumbnail-generator']?.ThumbnailGenerator
      const thumbGen = new ThumbnailGenerator(undefined, 256, 256);
      setThumbnailGenerator(thumbGen);
      return thumbGen;
    } else {
      return thumbnailGenerator;
    }
  } , [thumbnailGenerator, addWebgi]);

  return {
    preview,
    setPreview,
    viewer,
    setViewer,
    fileQueue,
    updateQueue,
    searchString,
    setSearchString,
    searchResults,
    setSearchResults,
    loading,
    setLoading,
    fileMap,
    setFileMap,
    getThumbnailGenerator,
    setThumbnailGenerator,
    dragging,
    setDragging,
    fileQueueRef,
    currentPath,
    setCurrentPath,
    uploadOptions,
    setUploadOptions,
    selectedFiles,
    setSelectedFiles,
    getAssets,
    addWebgi,
    showUploadProgress,
    setShowUploadProgress,
    currentView,
    setCurrentView,
    draggingRef,
    isSideBarOpen,
    setIsSideBarOpen,
  };
}

export function BrowserProvider({ children }: { children: any }) {
  const value = useSetupBrowser();
  return createElement(BrowserContext.Provider, { value }, children);
}

const processAssets = (files: CustomFileData[], withBasePath : boolean): AssetsLibrary => {
  const assets: AssetsLibrary = {};
  assets.presets = {};
  assets.materials = {};

  const mainFolderNames = ["presets", "hdrmaps", "materials", "maps", "configs", "images"];

  const getAssets = (files: CustomFileData[]) => {
    if (withBasePath) {
      return files.map((file) => `${file.file}` + ";" + `${file.thumb}`);
    }
    return files.map((file) => `${file.id}/${file.file}` + ";" + `${file.id}/${file.thumb}`);
  };

  mainFolderNames.forEach((folderName) => {
    const folder = files.find((file) => file.name === folderName);
    if (folder) {
      const children = files.filter((file) => file.path.includes(folder.id));
      switch (folderName) {
        case "presets":
          assets.presets!.VJSON = { basePath: "", assets: getAssets(children) };
          break;
        case "hdrmaps":{
          assets.presets!.Environment = { basePath: "", assets: getAssets(children.filter((file) => file.tags?.includes("metal"))) };
          assets.presets!.GemEnvironment = { basePath: "", assets: getAssets(children.filter((file) => file.tags?.includes("gem"))) };
          const remaining = children.filter((file) => !file.tags?.includes("metal") && !file.tags?.includes("gem"));
          if(remaining.length > 0) {
            assets.presets!.Environment.assets = {...assets.presets!.Environment.assets, ...getAssets(children)}
          }
          break;}
        case "materials":{
          assets.materials!.metal = { basePath: "", assets: getAssets(children.filter((file) => file.tags?.includes("metal"))) };
          assets.materials!.gem = { basePath: "", assets: getAssets(children.filter((file) => file.tags?.includes("gem"))) };
          assets.materials!.ceramic = { basePath: "", assets: getAssets(children.filter((file) => file.tags?.includes("ceramic"))) };
          assets.materials!.pearl = { basePath: "", assets: getAssets(children.filter((file) => file.tags?.includes("pearl"))) };
          const remaining = children.filter((file) => !file.tags?.includes("metal") && !file.tags?.includes("gem") && !file.tags?.includes("ceramic") && !file.tags?.includes("pearl"));
          if(remaining.length > 0) {
            assets.materials!.other = {
              basePath: "",
              assets: getAssets(remaining)
            };
          }
          break;}
        case "configs":
          assets.presets!.Ground = { basePath: "", assets: getAssets(children) };
          break;
        case "images":
          assets.presets!.Background = { basePath: "", assets: getAssets(children) };
          break;
        default:
          break;
      }
    }
  });

  return assets;
};
