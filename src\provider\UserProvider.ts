/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, createElement, useCallback, useContext, useMemo, useRef, useState } from "react";
import { CustomFileData, FileAPI } from "../api/api";
import { AppConfig, User } from "../components/shared/types";
import { UserLimits } from "../components/shared/limits";
import { isModelFile } from "../components/shared/util";
import toast from "react-hot-toast";

type UserFeatuer = "custom-logo" | "encryption" | "public-sharing" | "edit-tags" | "setup-user" | "edit-user" | "edit-file" | "upload-file" | "delete-file" | "delete-folder" 

interface IUserContext {
  isLogin: boolean;
  setIsLogin: (isLogin: boolean) => void;
  user: User | null;
  setUser: (user: User | null) => void;
  api: FileAPI | null;
  setApi: (api: FileAPI | null) => void;
  logOut: () => void;
  config: AppConfig | null;
  setConfig: (config: AppConfig | null) => void;
  can: (
    permission: User<PERSON>eatuer,
    currentUser?: User,
    params?: { file?: CustomFileData | null }
  ) => boolean;
  canUpload: (file: File) => Promise<string | undefined>;
  getModelsCount: (userId?: string) => Promise<number | null>;
  incrementModelsCount: () => Promise<number | null>;
  decrementModelsCount: () => Promise<number | null>;
  modelsCountRef: React.MutableRefObject<number | null>;
  isSuperAdmin: boolean;
  setupUser: () => Promise<{error?: string, user?: User}>;
  isSettingUpUserRef: React.MutableRefObject<boolean>;
  getPublicShareUrls: () => string[];
  isEnterpriseClient: () => boolean;
}

const UserContext = createContext<IUserContext>({} as IUserContext);

export const useUser = () => useContext(UserContext);

function useSetupUser() {
  const [user, setUser] = useState<User | null>(null);
  const [api, setApi] = useState<FileAPI | null>(FileAPI.getInstance());
  const [isLogin, setIsLogin] = useState<boolean>(false);
  const [config, setConfig] = useState<AppConfig | null>(null);
  const modelsCount = useRef<number | null>(null);
  const [, _setmodelsCount] = useState<number | null>(null); // to force re-render
  const isSettingUpUserRef = useRef(false);

  const logOut = useCallback(()=>{
    setIsLogin(false)
    setUser(null)
    api?.logOut()
    modelsCount.current = null;
    _setmodelsCount(null);
  }, [api])

  const isSuperAdmin = useMemo(() => user?.role === "superadmin" , [user]);

  const getModelsCount = useCallback(async (userId?: string) : Promise<number | null>=> {
    if (!user && !userId || !api) {
      console.error("User not found");
      return null;
    }

    if(modelsCount.current !== null) return modelsCount.current;

    const {data , error} = await api.getModelsCount(user?.id || userId);
    if(error || !data) {
      console.error(error)
      return null;
    }

    modelsCount.current = data.length;
    _setmodelsCount(modelsCount.current); // force re-render
    return data.length
  }, [api, user]);

  const incrementModelsCount = useCallback(async () : Promise<number | null>=> {
    if(modelsCount.current === null) {
      modelsCount.current = await getModelsCount();
      if(modelsCount.current === null) {
        console.error("Error getting models count");
        return null;
      }
    }
    modelsCount.current += 1;
    return modelsCount.current;
  }, [getModelsCount , modelsCount]);

  const decrementModelsCount = useCallback(async () : Promise<number | null>=> {
    if(modelsCount.current === null) {
      modelsCount.current = await getModelsCount();
      if(modelsCount.current === null) {
        console.error("Error getting models count");
        return null;
      }
    }
    modelsCount.current -= 1;
    return modelsCount.current;
  }, [getModelsCount , modelsCount]);

  const can = useCallback((permission: UserFeatuer , currentUser?: any , params?: {file?: CustomFileData | null}) => {
    if(!config) return false;
    const u = currentUser || user;
    const isAdmin = u?.role?.endsWith("admin");
    const isEditor = u?.role?.endsWith("editor");
    const isViewer = u?.role?.endsWith("viewer");
    const isGuest = u?.role === "guest";
    const isVerified = u?.verified;
    const isSuperAdmin = u?.role === "superadmin";
    switch (permission) {
      case "custom-logo":
        return Boolean(config.plan?.["allowed-features"]?.includes("custom-logo") && config?.logo);
      case "encryption":
        return Boolean(config.features?.encryption?.enabled);
      case "public-sharing":
        return Boolean(config.features?.["public-sharing"]?.enabled);
      case "edit-tags":
        return isAdmin || isEditor;
      case "setup-user":
        return Boolean(config.features?.["setup-user"]?.enabled && isGuest && !u.meta.base && isVerified)
      case "edit-user":
        return isAdmin;
      case "edit-file": {
        // Ensure the user is authenticated.
        if (!u?.id) return false;
        // Parse file meta if needed.
        let fileMeta = params?.file?.meta;
        if (typeof fileMeta === "string") {
          try {
            fileMeta = JSON.parse(fileMeta);
          } catch (e) {
            fileMeta = {};
          }
        }
        // Check role or file-specific write permission.
        const rolePermission =
          u.role === "editor" ||
          (typeof u.role === "string" && u.role.includes("admin")) ||
          (fileMeta?.w && Array.isArray(fileMeta.w) && fileMeta.w.includes(u.email));
        if (!rolePermission) return false;
        if (params?.file) {
          // Disallow editing if the file is locked.
          if (params.file.locked) return false;
          // If the user has a meta base, ensure the file id or path matches it.
          if (u.meta?.base) {
            const fileIdentifier = params?.file.path || "";
            if (!fileIdentifier.startsWith(u.meta.base)) return false;
          }
        }
        return true;
      }
      case "upload-file":
        return isAdmin || isEditor;
      case "delete-file": {
        if (!u?.id) return false;

        const rolePermission = isAdmin || isEditor;

        if (!rolePermission) return false;


        // Check features
        if (config.features?.["delete-file"]?.enabled === false) return false; //enabled by default


        if (params?.file?.locked) return false;

        return true;
      }
      case "delete-folder": {
        if (!u?.id) return false;
        const rolePermission = isAdmin || isEditor;

        if (!rolePermission) return false; 
        
        if (params?.file?.locked) return false;
        return true;
      }
      default:
        return false;
    }}, [config, user])

  const canUpload = useCallback(async (file: File): Promise<string | undefined> =>{
    if(!config) return "Config not found";
    if(!user) return "User not found";
    if(!config.plan) return "Plan not found";
    if(!user.role?.endsWith("admin") && user.role !== "editor") return "You don't have permission to upload files";
    const drivePlan = config?.plan;
    const userPlan = UserLimits[user?.meta?.plan || "free"];
    //Check file size limit
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > drivePlan['file-size-limit-mb']) {
      return `File size exceeds the drive plan limit of ${drivePlan['file-size-limit-mb']} MB.`;
    }
    if (userPlan && fileSizeMB > userPlan["maxFileSizeMb"]) {
      return `File size exceeds the user plan limit of ${userPlan["maxFileSizeMb"]} MB.`;
    }

    //Check number of files limit
    const modelsCount = await getModelsCount();

    if(modelsCount === null) {
      console.error("Error getting models count");
      return "Error getting models count";
    }
    
    if (isModelFile(file) && userPlan && modelsCount >= userPlan.maxNumberOfModels) {
      return `You have reached the maximum number of files allowed.`;
    }
  


    // Check allowed file types
    // const fileExtension = file.name.split('.').pop()?.toLowerCase();
    // if (!fileExtension || !plan['allowed-file-types'].includes(fileExtension)) {
    //   return `File type .${fileExtension} is not allowed. Allowed types: ${plan['allowed-file-types'].join(', ')}.`;
    // }
    
    return undefined; // Upload is allowed
  }, [config, getModelsCount, user]);

  const setupUser = useCallback(async () => {
    if (isSettingUpUserRef.current) return { error: "Already setting up user" };
    isSettingUpUserRef.current = true;
    toast.loading("Setting up user...", { id: "setupUser" });

    if (!api) {
      toast.error("Something went wrong", { id: "setupUser" });
      isSettingUpUserRef.current = false;
      return{ error: "API not found" };
    }

    const { user , error } = await api.setupUser();

    if (error || !user) {
      toast.error("Something went wrong while setting up user, please login again, if the problem persists, contact support", { id: "setupUser" });
      console.error("Error setting up user", error);
      setUser(null);
      setIsLogin(false);
      api.logOut();
      isSettingUpUserRef.current = false;
      return { error: "Error setting up user" };
    }
    
    toast.success("User setup complete", { id: "setupUser" });
    setUser(user);
    setIsLogin(true);
    isSettingUpUserRef.current = false;
    return { user };
  } , [api]);


  const getPublicShareUrls = useCallback(() => {
    if(config?.["public-viewer-link"]) return config["public-viewer-link"].split(",")
    if(!user?.meta?.share) return undefined;
    return typeof user.meta.share === "string" ? user.meta.share.split(",") : user.meta.share;
  }, [config, user]);

  const isEnterpriseClient = useCallback(() => {
    if (config?.plan?.name?.toLowerCase() === "enterprise") return true;
    return user?.meta?.plan === "enterprise";
  }, [config, user]);

  return {
    isLogin,
    setIsLogin,
    user,
    setUser,
    api,
    setApi,
    logOut,
    config,
    setConfig,
    can,
    canUpload,
    getModelsCount,
    incrementModelsCount,
    decrementModelsCount,
    modelsCountRef: modelsCount,
    isSuperAdmin,
    setupUser,
    isSettingUpUserRef,
    getPublicShareUrls,
    isEnterpriseClient,
  };
}

export function UserProvider({ children }: { children: any }) {
  const value = useSetupUser();
  return createElement(UserContext.Provider, { value }, children);
}
