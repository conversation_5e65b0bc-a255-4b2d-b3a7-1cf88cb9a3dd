import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useUser } from "../../provider/UserProvider.ts";
import toast from "react-hot-toast";
import { useLoaderData, useParams } from "react-router-dom";
import { FileAPI } from "../../api/api.ts";
import { Outlet, useLocation } from "react-router-dom";
import ModalProvider from "../../provider/ModalProvider.tsx";
import { Button, Input, Checkbox } from "@nextui-org/react";
import { validateEmail } from "../shared/util.ts";
import { useBrowser } from "../../provider/BrowserProvider.ts";
import Header from "./AuthHeader.tsx";
import LoginLogo from "../icons/LoginLogo.tsx";
import BaseLink from "../BaseLink.tsx";
import { CustomFileMapProvider } from "../../hooks/fileHooks.ts";
import { useBaseNavigate } from "../../hooks/UseBaseNavigate.tsx";
import EyeSlashFilledIcon from "../icons/EyeSlashFilledIcon.tsx";
import EyeFilledIcon from "../icons/EyeFilledIcon.tsx";

// Validation function that returns error message or undefined
function validateField(type: string, value: string, compareValue?: string): string | undefined {
  switch (type) {
    case "username":
      if (!value.trim()) return "Username is required";
      if (value.length < 3) return "Username must be at least 3 characters long";
      if (!(/^[A-Za-z][A-Za-z0-9]*$/).test(value)) return "Invalid username, it must start with a letter, contain only letters and numbers, and have no spaces."; 
      break;
    case "password":
      if (!value.trim()) return "Password is required";
      if (value.length < 8) return "Password must be at least 8 characters long";
      break;
    case "name":
      if (!value.trim()) return "Name is required";
      if (value.length < 3) return "name must be at least 3 characters long";
      break;
    case "email":
      if (!value.trim()) return "Email is required";
      if (!validateEmail(value)) return "Please enter a valid email";
      break;
    case "passwordConfirm":
      if (!value.trim()) return "Confirm password is required";
      if (value !== compareValue) return "Passwords do not match";
      break;
    default:
      break;
  }
  return undefined;
}

function Login() {
  const { setIsLogin, api, setUser, isLogin, setApi, setConfig, can, config, isSuperAdmin , logOut , setupUser } = useUser();
  const { setFileMap } = useBrowser();
  const [username, setUsername] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { folderId, basename } = useParams();
  const navigate = useBaseNavigate();
  const location = useLocation();
  const pageData: any = useLoaderData();
  const [modalContext , setModalContext] = useState<any>(null);
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);

  // for signup
  const [email, setEmail] = useState<string>("");
  const [passwordConfirm, setPasswordConfirm] = useState<string>("");
  const isSignup = location.pathname.includes("signup");

  // Add new state variables for checkboxes
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [newsletterAccepted, setNewsletterAccepted] = useState(false);

  // Error messages
  const usernameError = submitted && isSignup ? validateField("username", username) : undefined;
  const passwordError = submitted ? validateField("password", password) : undefined;
  const emailError = submitted  ? validateField("email", email) : undefined;
  const passwordConfirmError = submitted && isSignup ? validateField("passwordConfirm", passwordConfirm, password) : undefined;

  const handleModalContextChange = useCallback((context : any) => {
    setModalContext(context);
  }, [setModalContext]);

  //drive config
  useEffect(() => {
    if (pageData?.config) {
      setConfig(pageData?.config);
    }
  }, [pageData, setConfig]);

  const isLoginPage = useMemo(()=>location.pathname.includes("login")
    || (location.pathname === ("/" + basename)) || (location.pathname === ("/" + basename + "/"))
    || (location.pathname === "/" && api?.getBaseName() ? true : false)
  , [location.pathname, api, basename]);

  useEffect(() => {
    if ((!basename || (basename != "drive" && basename != "drive-dev")) && (location.pathname.includes("login") || location.pathname.includes("signup"))) {
      const isDev = window.origin.includes("dev.drive") || window.origin.includes("localhost");
      const newBasename = isDev ? 'drive-dev' : 'drive';
      const currentPath = location.pathname;
      if (!currentPath.includes(newBasename)) {
        navigate( newBasename + (currentPath === '/' ? '' : currentPath));
      }
    }
  }, [basename, location, navigate]);

  useEffect(() => {
    if (config?.["app-name"]) {
      document.title = config["app-name"];
    } else {
      document.title = "iJewel Drive";
    }

    //favicon
    const link: HTMLLinkElement = document.querySelector("link[rel*='icon']") || document.createElement('link');
    link.type = 'image/x-icon';
    link.rel = 'shortcut icon';
    link.href = can("custom-logo") && config?.favicon ? config.favicon : "https://drive.ijewel3d.com/logo-sm.svg"
    document.getElementsByTagName('head')[0].appendChild(link);

  }, [can, config])

  useEffect(() => {
    setSubmitted(false);
    if (api === null) {
      setApi(FileAPI.getInstance());
      return;
    }
    if (api.user !== null) {

      //user is not verified
      if(api.user.verified === false && !isSuperAdmin){
        if(isAuthRoute && !isLoginPage){
          toast.error("Please verify your email to login" , {id : "verify-email"});
          navigate("/verify-email");
          // setIsLogin(true);
        }else{
          setIsLogin(!isLoginPage);
          setUser(api.user);
        }
      }else if (api.user && api.user.verified === true && can("setup-user", api.user)){
        setupUser();
        return;
      } else {
        if (isLoginPage || isSignup) {
          folderId ? navigate(`/folders/${folderId}`) : navigate("/folders/");
        }
        setIsLogin(true);
        setUser(api.user);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [api, folderId, isLogin, isLoginPage, isSignup, location.pathname, navigate, setApi, setIsLogin, setUser]);

  useEffect(() => {
    setFileMap({});
  }, [isLogin, setFileMap]);

  const handleLogin = useCallback(async () => {
    setSubmitted(true);

    if (!api) {
      toast.error("API not initialized");
      return;
    }

    if(api.user || isLogin){
      logOut();
    }

    // Check terms and privacy policy acceptance for signup
    if (isSignup && (!termsAccepted || !privacyAccepted)) {
      toast.error("Please accept Terms of Service and Privacy Policy to continue");
      return;
    }

    // Re-validate fields
    const emailError = validateField("email", email);
    const passwordError = validateField("password", password);
    let passwordConfirmError, usernameError;

    if (isSignup) {
      usernameError = validateField("username", username);
      passwordConfirmError = validateField("passwordConfirm", passwordConfirm, password);
    }

    // Collect all errors
    const allErrors = [emailError, usernameError, passwordError, passwordConfirmError].filter(Boolean);

    if (allErrors.length > 0) {
      toast.error(allErrors[0] as string);
      return;
    }

    setLoading(true);

    const { error, user } = isSignup
      ? await api.signUp({
        username,
        name: username,
        email,
        password,
        passwordConfirm,
      })
      : await api.login(email, password);

    setLoading(false);

    if (error || !user) {
      toast.error(`${error}`);
      logOut(); // logout just in case
      return;
    } 

    if (isSignup) {
      toast.success("Signup success, please check your mail to verify your account", { duration: 20000 });
      navigate("login");
      setEmail("");
      setUsername("");
      setPassword("");
      setPasswordConfirm("");
      logOut(); // logout user after signup, user needs to verify email to login
      return;
    }

    setUser(user);

    if(user.verified === false && !isSuperAdmin){  //redirect to verify email page
      toast.error("Please verify your email to login" , {id : "verify-email"});
      navigate("/verify-email");
      return;
    } else if (can("setup-user" , user)) { // if user is verified but the user is not setup yet
      const {error , user } = await setupUser();
      if(!error && user) navigate("/folders");
      return;
    }
    
    if (location.state && location.state.redirect) {
      navigate(location.state.redirect);
      return;
    }

    toast.success("Login success");
    setIsLogin(true);
    setUsername("");
    setEmail("");
    setPassword("");
  }, [api, isLogin, isSignup, termsAccepted, privacyAccepted, email, password, username, passwordConfirm, logOut, setUser, isSuperAdmin, can, location.state, setIsLogin, navigate, setupUser]);

  

  const isAuthRoute = location.pathname.includes("signup") || location.pathname.includes("folders") || isLoginPage 

  return isAuthRoute && !isLogin ? (
    <>
      <Header />
      <div className="w-screen min-h-screen flex flex-col items-center justify-center bg-[#F6F6F6]">
        <div className="w-[400px] flex flex-col relative p-7">
          <div className="flex items-center justify-center mb-4 relative">
            <LoginLogo className="h-8" />
          </div>

          <h1 className="text-2xl font-[700] mb-6 text-center">
            {isSignup ? "Sign up" : "Log in"}
          </h1>

          <form
            className="flex flex-col gap-4"
            onSubmit={(e) => {
              e.preventDefault();
              handleLogin();
            }}
            onInvalid={(e) => {
              e.preventDefault();
              setSubmitted(true);
            }}
          >
            {isSignup && <div className="flex gap-2 items-center h-[38px]">
              <label className="flex text-sm text-gray-700 w-[100px] justify-end font-semibold">Username</label>
              <Input
                required
                onValueChange={setUsername}
                value={username}
                placeholder="Enter your username"
                variant="bordered"
                radius="full"
                classNames={{
                  base: "w-full",
                  input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                  inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                  innerWrapper: "pr-4",
                  label: "text-sm text-gray-700"
                }}
                isInvalid={!!usernameError}
                errorMessage={usernameError}
              />
            </div>}

            {<div className="flex gap-2 items-center h-[38px]">
              <label className="flex text-sm text-gray-700 w-[100px] justify-end font-semibold">Email</label>
              <Input
                required
                onValueChange={setEmail}
                value={email}
                placeholder="Enter your email"
                variant="bordered"
                radius="full"
                classNames={{
                  base: "w-full",
                  input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                  inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                  innerWrapper: "pr-4",
                  label: "text-sm text-gray-700"
                }}
                type="email"
                autoComplete="email"
                isInvalid={!!emailError}
                errorMessage={emailError}
              />
            </div>}

            <div className="flex gap-2 items-center">
              <label className={"flex text-sm text-gray-700 w-[100px] justify-end font-semibold" + (isSignup ? " pb-6" : "")}>Password</label>
              <div className="w-full">
                <div>
                  <Input
                    onValueChange={setPassword}
                    value={password}
                    placeholder="Enter your password"
                    type={passwordVisible ? "text":"password"}
                    variant="bordered"
                    radius="full"
                    classNames={{
                      base: "w-full",
                      input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                      inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                      
                      label: "text-sm text-gray-700"
                    }}
                    endContent={
                      <button
                      aria-label="toggle password visibility"
                      className="focus:outline-none"
                      type="button"
                      onClick={()=>{setPasswordVisible(!passwordVisible)}}
                    >
                      {passwordVisible ? (
                        <EyeSlashFilledIcon className="text-2xl text-default-400 pointer-events-none" />
                      ) : (
                        <EyeFilledIcon className="text-2xl text-default-400 pointer-events-none" />
                      )}
                    </button>
                    }
                    required
                    isInvalid={!!passwordError}
                    errorMessage={passwordError}
                  />
                  {/* {isSignup && <div className="text-[11px] pt-2 px-[10px] text-gray-400">Must be at least 8 characters.</div>} */}
                </div>
              </div>
            </div>

            {!isSignup && (
              <div className="text-right">
                <BaseLink to="/forgot-password" className="text-[#6E72EA] text-sm">
                  Forgot Password?
                </BaseLink>
              </div>
            )}

            {isSignup && (
                <div className="flex gap-2 items-center">
                  <label className="flex text-sm text-gray-700 w-[100px] justify-end font-semibold">Confirm Password</label>
                  <Input
                      onValueChange={setPasswordConfirm}
                      value={passwordConfirm}
                      placeholder="Confirm password"
                      type="password"
                      variant="bordered"
                      radius="full"
                      classNames={{
                        base: "w-full",
                        input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                        inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                        innerWrapper: "pr-4",
                        label: "text-sm text-gray-700"
                      }}
                      required
                      isInvalid={!!passwordConfirmError}
                      errorMessage={passwordConfirmError}
                  />
                  </div>
            )}


            {isSignup && (
              <div className="flex flex-col gap-4 pl-8">
                <Checkbox size="sm"
                  radius="full"
                  color="primary"
                  isSelected={termsAccepted}
                  onValueChange={setTermsAccepted}
                  classNames={{
                    label: "text-gray-700 text-sm"
                  }}
                >
                  I agree to the <BaseLink to="/terms" className="text-[#6E72EA] underline text-sm">Terms of Service</BaseLink> *
                </Checkbox>
                <Checkbox size="sm"
                  radius="full"
                  color="primary"
                  isSelected={privacyAccepted}
                  onValueChange={setPrivacyAccepted}
                  classNames={{
                    label: "text-gray-700 text-sm"
                  }}
                >
                  I agree to the <BaseLink to="/terms" className="text-[#6E72EA] underline text-sm">Privacy Policy</BaseLink> *
                </Checkbox>
                <Checkbox size="sm"
                  radius="full"
                  color="primary"
                  isSelected={newsletterAccepted}
                  onValueChange={setNewsletterAccepted}
                  classNames={{
                    label: "text-gray-700 text-sm"
                  }}
                >
                  Send tips, tutorials and news to my inbox
                </Checkbox>
              </div>
            )}

            <Button
              type="submit"
              isLoading={loading}
              isDisabled={loading}
              className="h-11 w-full rounded-full bg-[#6E72EA] text-white font-medium"
            >
              {isSignup ? "Sign up" : "Log in"}
            </Button>

            {/* <Button
              variant="bordered"
              className="h-12 w-full rounded-full border-gray-300 font-normal"
              startContent={
                <img src="https://www.google.com/favicon.ico" className="w-5 h-5" alt="Google" />
              }
            >
              {isSignup ? "Sign up with Google" : "Log in with Google"}
            </Button> */}

            <div className="text-center mt-2">
              <span className="text-[#686868] text-sm">
                {isSignup ? "Already have an account? " : "Don't have an account? "}
              </span>
              <BaseLink to={isSignup ? "/login" : "/signup"} className="text-[#6E72EA] text-sm">
                {isSignup ? "Log in" : "Sign up"}
              </BaseLink>
            </div>
          </form>
        </div>
      </div>
    </>
  ) : (
    <CustomFileMapProvider modalContext={modalContext}>
      <ModalProvider onProviderChange={handleModalContextChange}>
        <Outlet />
      </ModalProvider>
    </CustomFileMapProvider>
  );
}

export default Login;
