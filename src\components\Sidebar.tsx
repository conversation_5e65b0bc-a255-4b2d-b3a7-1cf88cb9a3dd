import { useState, FC, useMemo, useEffect } from 'react';
import Home from "./icons/Home";
import HardDrive from "./icons/Drive";
import Trash from "./icons/Trash";
import Cloud from "./icons/Cloud";
import Left from "./icons/Left.tsx";
import { But<PERSON> } from "./Button.tsx";
import { useCustomFileMap } from "../hooks/fileHooks";
import { AddNewButton } from './AddNewButton';
import { ViewType } from "../types/views";
import { UserLimits } from './shared/limits.ts';
import { useUser } from '../provider/UserProvider.ts';

declare module 'react' {
    interface InputHTMLAttributes<T> extends HTMLAttributes<T> {
        directory?: string;
        webkitdirectory?: string;
    }
}

interface SidebarProps {
  onViewChange: (view: ViewType) => void;
  currentView: ViewType;
}

export const Sidebar: FC<SidebarProps> = ({ onViewChange, currentView }) => {
    const [isExpanded, setIsExpanded] = useState(true);
    const { uploadFiles } = useCustomFileMap();
    const {user , api, getModelsCount , modelsCountRef} = useUser();
    // const [storageUsed, setStorageUsed] = useState<number | null>(null);
    useEffect(() => {
        if (!user || !api) return;

        getModelsCount() // this will update the modelsCountRef.current

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [api, user , modelsCountRef.current]);

    const storageLimit = useMemo(() => UserLimits[user?.meta?.plan ?? 'free']?.maxNumberOfModels || 0, [user]);
    // const storageLimit = 

    const storagePercentage = useMemo(() => {
        if (!user || !api || modelsCountRef.current === null) return 0;
        return (modelsCountRef.current / storageLimit) * 100;
        
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user, api, modelsCountRef.current, storageLimit]);

    return (
        <div className="relative md:flex h-full hidden">
            <div className={`p-4 flex flex-col transition-all duration-300 ease-in-out ${
                isExpanded ? 'w-60' : 'w-20'
            }`}>
                <AddNewButton 
                    compact={!isExpanded}
                    className="mb-4"
                    onUpload={uploadFiles}
                />

                {/* Company Name */}
                <h1 className={`text-xl mt-2 font-semibold  text-gray-800 mb-8 transition-opacity duration-200 ${
                    !isExpanded && 'opacity-0 h-0 mb-0'
                }`}>
                    {user?.user ?? "iJewel Company"}
                </h1>

                {/* Navigation Links */}
                <nav className="flex-1 mr-2">
                    <ul className="space-y-2">
                        <li>
                            <Button
                                name={
                                    <div className="flex items-center gap-3">
                                        <Home className={`w-5 h-5 min-w-5 ${
                                            currentView === 'home' ? 'text-white' : 'text-[#373737]'
                                        }`}/>
                                        {isExpanded && <span className="text-sm">Home</span>}
                                    </div>
                                }
                                color={currentView === 'home' ? 'primary' : undefined}
                                varient={currentView === 'home' ? undefined : 'light'}
                                className="justify-start rounded-3xl h-8"
                                fullWidth
                                size="lg"
                                onClick={() => onViewChange('home')}
                            />
                        </li>
                        <li>
                            <Button
                                name={
                                    <div className="flex items-center gap-3">
                                        <HardDrive className={`w-5 h-5 min-w-5 ${
                                            currentView === 'files' ? 'text-white' : 'text-primary'
                                        }`}/>
                                        {isExpanded && <span className="text-sm">My Drive</span>}
                                    </div>
                                }
                                color={currentView === 'files' ? 'primary' : undefined}
                                varient={currentView === 'files' ? undefined : 'light'}
                                className="justify-start rounded-3xl h-8"
                                fullWidth
                                size="lg"
                                onClick={() => onViewChange('files')}
                            />
                        </li>
                        <li>
                            <Button
                                name={
                                    <div className="flex items-center gap-3">
                                        <Trash className={`w-5 h-5 min-w-5 ${
                                            currentView === 'trash' ? 'text-white' : 'text-[#373737]'
                                        }`}/>
                                        {isExpanded && <span className="text-sm">Deleted files</span>}
                                    </div>
                                }
                                color={currentView === 'trash' ? 'primary' : undefined}
                                varient={currentView === 'trash' ? undefined : 'light'}
                                className="justify-start rounded-3xl h-8"
                                fullWidth
                                size="lg"
                                onClick={() => onViewChange('trash')}
                            />
                        </li>
                    </ul>
                </nav>

                {/*<div className="border-t border-[#D9D9D9] p-5"></div>*/}

                {/* Storage Section */}
                {modelsCountRef.current !== null && <div className="mt-auto mb-3 ml-2">
                    <div className="flex items-center gap-3 text-gray-700 mb-2 ml-1">
                        <Cloud className="w-5 h-5 min-w-5"/>
                        {isExpanded && <span className="font-medium text-sm">Available Storage</span>}
                    </div>

                    <div className="h-1 bg-[#C0C0C0] rounded-3xl overflow-hidden mt-5 mr-2">
                        <div
                            className={`h-full rounded-full ${storagePercentage > 95 ? 'bg-danger' : storagePercentage > 75 ? 'bg-warning' : 'bg-primary'}`}
                            style={{width: `${storagePercentage}%`}}
                        />
                    </div>

                    {isExpanded && (
                        <p className="text-gray-600 mt-2 h-6 text-sm">
                            {modelsCountRef.current}/{storageLimit} Models used
                        </p>
                    )}
                </div>}
            </div>

            {/* Toggle button */}
            <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="absolute -right-3 top-1/2 transform -translate-y-1/2 
                           w-6 h-6 bg-white rounded-full shadow-md flex items-center 
                           justify-center z-10 hover:bg-gray-50"
            >
                <Left
                    className={`w-4 h-4 text-gray-400 transition-transform duration-300 
                               ${isExpanded ? '' : 'rotate-180'}`}
                />
            </button>
        </div>
    );
};